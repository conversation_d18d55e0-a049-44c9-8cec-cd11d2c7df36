import { auth } from "@/server/auth";

export default async function TestAuth() {
  const session = await auth();

  return (
    <div className="min-h-screen bg-gray-100 p-8">
      <div className="max-w-2xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">认证测试页面</h1>
        
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">Session 信息:</h2>
          
          {session ? (
            <div className="space-y-4">
              <div>
                <strong>已登录</strong>
              </div>
              <div>
                <strong>用户ID:</strong> {session.user?.id}
              </div>
              <div>
                <strong>用户名:</strong> {session.user?.name}
              </div>
              <div>
                <strong>邮箱:</strong> {session.user?.email}
              </div>
              {session.user?.image && (
                <div>
                  <strong>头像:</strong>
                  <img 
                    src={session.user.image} 
                    alt="用户头像" 
                    className="w-16 h-16 rounded-full mt-2"
                  />
                </div>
              )}
              <div className="mt-4">
                <a 
                  href="/api/auth/signout"
                  className="bg-red-500 text-white px-4 py-2 rounded hover:bg-red-600"
                >
                  退出登录
                </a>
              </div>
            </div>
          ) : (
            <div className="space-y-4">
              <div>
                <strong>未登录</strong>
              </div>
              <div>
                <a 
                  href="/api/auth/signin"
                  className="bg-blue-500 text-white px-4 py-2 rounded hover:bg-blue-600"
                >
                  使用 Google 登录
                </a>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
