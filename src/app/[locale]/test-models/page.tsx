"use client";

import { useState } from "react";
import { api } from "@/trpc/react";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { signIn } from "next-auth/react";
import { Co<PERSON>, Sparkles, Info, CheckCircle } from "lucide-react";
import { ModelSelector } from "@/components/ModelSelector";

export default function TestModelsPage() {
  const { data: session } = useSession();
  const [selectedModelId, setSelectedModelId] = useState<string>("");

  // 获取可用模型列表
  const { data: availableModels, isLoading: modelsLoading } = api.creation.getAvailableModels.useQuery({
    category: 'image-editing'
  });

  // 获取用户积分信息
  const { data: userWithCredits } = api.credits.getUserWithCredits.useQuery(
    undefined,
    { enabled: !!session }
  );

  // 检查当前模型的积分需求
  const { data: creditCheck } = api.creation.checkCreditsForCreation.useQuery({
    modelId: selectedModelId || undefined
  }, {
    enabled: !!session && !!selectedModelId
  });

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">请先登录</h1>
          <Button onClick={() => signIn("google")}>
            使用 Google 登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">AI模型配置测试</h1>
        
        {/* 用户积分信息 */}
        {userWithCredits && (
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
              <Coins className="h-5 w-5 text-amber-500" />
              积分信息
            </h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div className="bg-green-50 p-4 rounded-lg">
                <p className="text-sm text-green-600">可用积分</p>
                <p className="text-2xl font-bold text-green-800">
                  {userWithCredits.credits?.availableCredits || 0}
                </p>
              </div>
              <div className="bg-blue-50 p-4 rounded-lg">
                <p className="text-sm text-blue-600">总积分</p>
                <p className="text-2xl font-bold text-blue-800">
                  {userWithCredits.credits?.totalCredits || 0}
                </p>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <p className="text-sm text-gray-600">用户类型</p>
                <p className="text-lg font-semibold text-gray-800">
                  {userWithCredits.userType === 'free' ? '免费用户' : '付费用户'}
                </p>
              </div>
            </div>
          </div>
        )}

        {/* 模型选择器测试 */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Sparkles className="h-5 w-5 text-rose-500" />
            模型选择器
          </h2>
          
          {modelsLoading ? (
            <p>加载模型列表中...</p>
          ) : availableModels && availableModels.length > 0 ? (
            <div className="space-y-4">
              <ModelSelector
                models={availableModels}
                selectedModelId={selectedModelId || availableModels[0]?.id || ""}
                onModelSelect={setSelectedModelId}
              />
              
              {/* 当前选择的模型信息 */}
              {selectedModelId && (
                <div className="bg-gray-50 p-4 rounded-lg">
                  <h3 className="font-medium mb-2">当前选择的模型:</h3>
                  <p className="text-sm text-gray-600">模型ID: {selectedModelId}</p>
                  {creditCheck && (
                    <div className="mt-2 space-y-1">
                      <p className="text-sm">
                        <span className="text-gray-600">积分需求:</span>
                        <span className="font-medium ml-1">{creditCheck.requiredCredits}</span>
                      </p>
                      <p className="text-sm">
                        <span className="text-gray-600">积分充足:</span>
                        <span className={`font-medium ml-1 ${
                          creditCheck.hasEnoughCredits ? 'text-green-600' : 'text-red-600'
                        }`}>
                          {creditCheck.hasEnoughCredits ? '是' : '否'}
                        </span>
                      </p>
                    </div>
                  )}
                </div>
              )}
            </div>
          ) : (
            <p className="text-gray-500">暂无可用模型</p>
          )}
        </div>

        {/* 模型列表详情 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4 flex items-center gap-2">
            <Info className="h-5 w-5 text-blue-500" />
            模型详情列表
          </h2>
          
          {availableModels && availableModels.length > 0 ? (
            <div className="space-y-4">
              {availableModels.map((model) => (
                <div 
                  key={model.id} 
                  className={`border rounded-lg p-4 transition-all ${
                    model.id === selectedModelId 
                      ? 'border-rose-300 bg-rose-50' 
                      : 'border-gray-200 hover:border-gray-300'
                  }`}
                >
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <h3 className="font-semibold text-gray-800">
                          {model.name}
                        </h3>
                        {model.id === selectedModelId && (
                          <CheckCircle className="h-4 w-4 text-rose-500" />
                        )}
                      </div>
                      <p className="text-gray-600 text-sm mb-3">
                        {model.description}
                      </p>
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1 text-amber-600">
                          <Coins className="h-4 w-4" />
                          <span>{model.creditsPerUse} 积分/次</span>
                        </div>
                        <div className="text-gray-500">
                          <span>类别: {model.category}</span>
                        </div>
                        <div className="text-gray-500">
                          <span>提供商: {model.provider}</span>
                        </div>
                      </div>
                    </div>
                    <Button
                      size="sm"
                      variant={model.id === selectedModelId ? "default" : "outline"}
                      onClick={() => setSelectedModelId(model.id)}
                    >
                      {model.id === selectedModelId ? "已选择" : "选择"}
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">暂无模型数据</p>
          )}
        </div>

        {/* 积分检查结果 */}
        {creditCheck && (
          <div className="bg-white p-6 rounded-lg shadow mt-6">
            <h2 className="text-xl font-semibold mb-4">积分检查结果</h2>
            <div className="bg-gray-50 p-4 rounded-lg">
              <pre className="text-sm">
                {JSON.stringify(creditCheck, null, 2)}
              </pre>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
