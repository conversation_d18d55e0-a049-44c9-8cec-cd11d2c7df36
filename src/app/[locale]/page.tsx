import { Navbar } from "./components/Navbar";
import { HeroSection } from "./components/HeroSection";
import { FeaturesSection } from "./components/FeaturesSection";
import { PricingSection } from "./components/PricingSection";
import { Footer } from "./components/Footer";
import { auth } from "@/server/auth";
import { BackgroundBlurs } from "./components/BackgroundBlurs";
import { Metadata } from "next";

export const metadata: Metadata = {
  title: 'Flux Pix - AI 图片编辑器首页',
  description: 'Flux Pix - 专业的 AI 图片编辑平台，免费提供智能去水印、背景替换、对象移除等功能。上传图片即刻体验 AI 修图，支持 JPG、PNG 格式，快速精准的在线图片处理工具。',
  keywords: ['AI图片编辑首页', '免费去水印', '在线背景替换', '智能图片处理', '一键修图', 'AI修图工具', '图片编辑平台', '在线P图'],
  openGraph: {
    title: 'Flux Pix - AI 图片编辑器首页',
    description: 'Flux Pix - 专业的 AI 图片编辑平台，免费提供智能去水印、背景替换、对象移除等功能。上传图片即刻体验 AI 修图。',
    type: 'website',
    url: 'https://fluxpix.ai',
    images: ['/og-image.png']
  },
  twitter: {
    card: 'summary_large_image',
    title: 'Flux Pix - AI 图片编辑器首页',
    description: 'Flux Pix - 专业的 AI 图片编辑平台，免费提供智能去水印、背景替换、对象移除等功能。',
    images: ['/og-image.png']
  },
  alternates: {
    canonical: 'https://fluxpix.ai'
  }
};

export default async function Home() {
  const session = await auth();

  return (
    <main className="min-h-screen bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50">
      <BackgroundBlurs />
      
      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 sm:px-6">
        <div className="pt-16">
          <HeroSection />
          <FeaturesSection />
          <PricingSection />
        </div>
      </div>

      <Footer />
    </main>
  );
}
