"use client";

import { useState } from "react";
import { api } from "@/trpc/react";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { signIn } from "next-auth/react";
import { <PERSON>ader2, <PERSON><PERSON><PERSON>, AlertCircle } from "lucide-react";

export default function TestGenerationPage() {
  const { data: session } = useSession();
  const [prompt, setPrompt] = useState("remove background");
  const [imageUrl, setImageUrl] = useState("https://images.unsplash.com/photo-1506905925346-21bda4d32df4?w=500");
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<any>(null);
  const [error, setError] = useState<string | null>(null);

  // 获取用户积分信息
  const { data: userWithCredits, refetch: refetchCredits } = api.credits.getUserWithCredits.useQuery(
    undefined,
    { enabled: !!session }
  );

  // 创建图片处理mutation
  const createMutation = api.creation.create.useMutation({
    onSuccess: (data) => {
      setResult(data);
      setError(null);
      setIsProcessing(false);
      void refetchCredits();
    },
    onError: (error) => {
      setError(error.message);
      setResult(null);
      setIsProcessing(false);
      void refetchCredits();
    },
  });

  const handleGenerate = async () => {
    if (!prompt.trim() || !imageUrl.trim()) {
      setError("请输入编辑指令和图片URL");
      return;
    }

    setIsProcessing(true);
    setError(null);
    setResult(null);

    try {
      await createMutation.mutateAsync({
        prompt: prompt.trim(),
        imageUrl: imageUrl.trim(),
      });
    } catch (error) {
      // 错误已在onError中处理
    }
  };

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">请先登录</h1>
          <Button onClick={() => signIn("google")}>
            使用 Google 登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">AI图片生成测试</h1>
        
        {/* 用户积分信息 */}
        {userWithCredits && (
          <div className="bg-white p-6 rounded-lg shadow mb-6">
            <h2 className="text-xl font-semibold mb-4">积分信息</h2>
            <div className="space-y-2">
              <p><strong>用户类型:</strong> {userWithCredits.userType === 'free' ? '免费用户' : '付费用户'}</p>
              {userWithCredits.credits && (
                <>
                  <p><strong>可用积分:</strong> {userWithCredits.credits.availableCredits}</p>
                  <p><strong>总积分:</strong> {userWithCredits.credits.totalCredits}</p>
                  <p><strong>已使用:</strong> {userWithCredits.credits.usedCredits}</p>
                </>
              )}
            </div>
          </div>
        )}

        {/* 输入区域 */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">生成参数</h2>
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">编辑指令</label>
              <input
                type="text"
                value={prompt}
                onChange={(e) => setPrompt(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg"
                placeholder="例如: remove background, enhance quality"
              />
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">图片URL</label>
              <input
                type="url"
                value={imageUrl}
                onChange={(e) => setImageUrl(e.target.value)}
                className="w-full p-3 border border-gray-300 rounded-lg"
                placeholder="https://example.com/image.jpg"
              />
            </div>
            <Button
              onClick={handleGenerate}
              disabled={isProcessing || !userWithCredits?.credits || userWithCredits.credits.availableCredits < 1}
              className="w-full"
            >
              {isProcessing ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  处理中...
                </>
              ) : (
                <>
                  <Sparkles className="mr-2 h-4 w-4" />
                  开始生成 (消耗1积分)
                </>
              )}
            </Button>
          </div>
        </div>

        {/* 错误信息 */}
        {error && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
            <div className="flex items-center">
              <AlertCircle className="h-5 w-5 text-red-500 mr-2" />
              <span className="text-red-700">{error}</span>
            </div>
          </div>
        )}

        {/* 结果显示 */}
        {result && (
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-xl font-semibold mb-4">生成结果</h2>
            <div className="space-y-4">
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <h3 className="font-medium mb-2">原始图片</h3>
                  <img 
                    src={result.originalImageUrl} 
                    alt="原始图片" 
                    className="w-full h-64 object-cover rounded-lg border"
                  />
                </div>
                <div>
                  <h3 className="font-medium mb-2">生成结果</h3>
                  <img 
                    src={result.generatedImageUrl} 
                    alt="生成结果" 
                    className="w-full h-64 object-cover rounded-lg border"
                  />
                </div>
              </div>
              <div className="bg-gray-50 p-4 rounded-lg">
                <h3 className="font-medium mb-2">处理信息</h3>
                <div className="text-sm space-y-1">
                  <p><strong>指令:</strong> {result.prompt}</p>
                  <p><strong>消耗积分:</strong> {result.creditsUsed}</p>
                  <p><strong>处理时间:</strong> {result.processingTime}秒</p>
                  <p><strong>生成图片URL:</strong> 
                    <a href={result.generatedImageUrl} target="_blank" rel="noopener noreferrer" className="text-blue-500 hover:underline ml-1">
                      查看原图
                    </a>
                  </p>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
