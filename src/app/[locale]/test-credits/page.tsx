"use client";

import { useState } from "react";
import { api } from "@/trpc/react";
import { Button } from "@/components/ui/button";
import { useSession } from "next-auth/react";
import { signIn } from "next-auth/react";

export default function TestCreditsPage() {
  const { data: session } = useSession();
  const [refreshing, setRefreshing] = useState(false);
  const [consuming, setConsuming] = useState(false);

  // 获取用户积分信息
  const { data: userWithCredits, refetch: refetchCredits } = api.credits.getUserWithCredits.useQuery(
    undefined,
    { enabled: !!session }
  );

  // 获取积分历史
  const { data: history, refetch: refetchHistory } = api.credits.getCreditHistory.useQuery(
    { limit: 10 },
    { enabled: !!session }
  );

  // 刷新每日积分
  const refreshMutation = api.credits.refreshDailyCredits.useMutation({
    onSuccess: () => {
      void refetchCredits();
      void refetchHistory();
      setRefreshing(false);
    },
    onError: (error) => {
      console.error("刷新积分失败:", error);
      setRefreshing(false);
    },
  });

  // 消耗积分
  const consumeMutation = api.credits.consumeCredits.useMutation({
    onSuccess: () => {
      void refetchCredits();
      void refetchHistory();
      setConsuming(false);
    },
    onError: (error) => {
      console.error("消耗积分失败:", error);
      setConsuming(false);
    },
  });

  const handleRefresh = () => {
    setRefreshing(true);
    refreshMutation.mutate();
  };

  const handleConsume = () => {
    setConsuming(true);
    consumeMutation.mutate({
      amount: 1,
      description: "测试消耗积分",
    });
  };

  if (!session) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-4">请先登录</h1>
          <Button onClick={() => signIn("google")}>
            使用 Google 登录
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-4xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">积分系统测试</h1>
        
        {/* 用户信息 */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">用户信息</h2>
          <p><strong>用户ID:</strong> {session.user.id}</p>
          <p><strong>用户名:</strong> {session.user.name}</p>
          <p><strong>邮箱:</strong> {session.user.email}</p>
        </div>

        {/* 积分信息 */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">积分信息</h2>
          {userWithCredits ? (
            <div className="space-y-2">
              <p><strong>用户类型:</strong> {userWithCredits.userType === 'free' ? '免费用户' : '付费用户'}</p>
              {userWithCredits.credits ? (
                <>
                  <p><strong>总积分:</strong> {userWithCredits.credits.totalCredits}</p>
                  <p><strong>已使用积分:</strong> {userWithCredits.credits.usedCredits}</p>
                  <p><strong>可用积分:</strong> {userWithCredits.credits.availableCredits}</p>
                  <p><strong>每日积分:</strong> {userWithCredits.credits.dailyCredits}</p>
                  <p><strong>上次刷新时间:</strong> {new Date(userWithCredits.credits.lastDailyRefresh).toLocaleString()}</p>
                </>
              ) : (
                <p>积分信息加载中...</p>
              )}
            </div>
          ) : (
            <p>加载中...</p>
          )}
          
          <div className="mt-4 space-x-4">
            <Button 
              onClick={handleRefresh} 
              disabled={refreshing}
            >
              {refreshing ? "刷新中..." : "刷新每日积分"}
            </Button>
            <Button 
              onClick={handleConsume} 
              disabled={consuming}
              variant="destructive"
            >
              {consuming ? "消耗中..." : "消耗1积分"}
            </Button>
          </div>
        </div>

        {/* 积分历史 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">积分历史</h2>
          {history && history.length > 0 ? (
            <div className="space-y-2">
              {history.map((transaction) => (
                <div key={transaction.id} className="border-b pb-2">
                  <div className="flex justify-between items-center">
                    <span className={`font-medium ${
                      transaction.amount > 0 ? 'text-green-600' : 'text-red-600'
                    }`}>
                      {transaction.amount > 0 ? '+' : ''}{transaction.amount} 积分
                    </span>
                    <span className="text-sm text-gray-500">
                      {new Date(transaction.createdAt).toLocaleString()}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{transaction.description}</p>
                  <p className="text-xs text-gray-400">类型: {transaction.type}</p>
                </div>
              ))}
            </div>
          ) : (
            <p>暂无积分历史</p>
          )}
        </div>
      </div>
    </div>
  );
}
