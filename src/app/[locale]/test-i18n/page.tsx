"use client";

import { useState } from "react";
import { useTranslations, useLocale } from "next-intl";
import { Button } from "@/components/ui/button";
import { useRouter } from "next/navigation";

export default function TestI18nPage() {
  const t = useTranslations();
  const creditsT = useTranslations("credits");
  const generationT = useTranslations("generation");
  const locale = useLocale();
  const router = useRouter();

  const [selectedNamespace, setSelectedNamespace] = useState("credits");

  const switchLocale = (newLocale: string) => {
    const currentPath = window.location.pathname;
    const newPath = currentPath.replace(`/${locale}`, `/${newLocale}`);
    router.push(newPath);
  };

  const testKeys = {
    credits: [
      "title",
      "currentCredits",
      "totalCredits",
      "usedCredits",
      "availableCredits",
      "userType.title",
      "userType.free",
      "userType.premium",
      "userType.pro",
      "history.title",
      "history.type.daily_grant",
      "history.type.usage",
      "history.type.refund",
      "insufficient.title",
      "insufficient.dailyRefresh",
      "insufficient.tryLater",
      "display.perEdit",
      "display.refreshedToday"
    ],
    generation: [
      "title",
      "processing",
      "success",
      "failed",
      "errors.invalidPrompt",
      "errors.invalidImageUrl",
      "errors.aiProcessingFailed",
      "errors.insufficientCredits",
      "errors.genericError",
      "result.originalImage",
      "result.generatedImage",
      "result.prompt",
      "result.creditsUsed",
      "status.aiProcessing",
      "status.uploadingToStorage",
      "status.completed"
    ],
    generate: [
      "title",
      "errors.uploadAndPrompt",
      "errors.processingFailed",
      "errors.invalidFileType",
      "errors.fileSizeLimit",
      "success.imageProcessed",
      "success.imageUploaded",
      "image.upload",
      "image.dropOrClick",
      "image.formats",
      "image.maxSize",
      "instructions.label",
      "actions.generate",
      "status.processing"
    ]
  };

  const getTranslationFunction = (namespace: string) => {
    switch (namespace) {
      case "credits":
        return creditsT;
      case "generation":
        return generationT;
      default:
        return t;
    }
  };

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8">国际化测试页面</h1>
        
        {/* 语言切换 */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">语言切换</h2>
          <div className="flex gap-4">
            <p className="text-gray-600">当前语言: <span className="font-semibold">{locale}</span></p>
            <Button
              onClick={() => switchLocale("zh")}
              variant={locale === "zh" ? "default" : "outline"}
              size="sm"
            >
              中文
            </Button>
            <Button
              onClick={() => switchLocale("en")}
              variant={locale === "en" ? "default" : "outline"}
              size="sm"
            >
              English
            </Button>
          </div>
        </div>

        {/* 命名空间选择 */}
        <div className="bg-white p-6 rounded-lg shadow mb-6">
          <h2 className="text-xl font-semibold mb-4">翻译命名空间</h2>
          <div className="flex gap-2 flex-wrap">
            {Object.keys(testKeys).map((namespace) => (
              <Button
                key={namespace}
                onClick={() => setSelectedNamespace(namespace)}
                variant={selectedNamespace === namespace ? "default" : "outline"}
                size="sm"
              >
                {namespace}
              </Button>
            ))}
          </div>
        </div>

        {/* 翻译测试 */}
        <div className="bg-white p-6 rounded-lg shadow">
          <h2 className="text-xl font-semibold mb-4">
            翻译测试 - {selectedNamespace}
          </h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {testKeys[selectedNamespace as keyof typeof testKeys]?.map((key) => {
              const translationFunc = getTranslationFunction(selectedNamespace);
              let translatedText;
              
              try {
                if (selectedNamespace === "credits" || selectedNamespace === "generation") {
                  translatedText = translationFunc(key);
                } else {
                  translatedText = t(`${selectedNamespace}.${key}`);
                }
              } catch (error) {
                translatedText = `[翻译错误: ${key}]`;
              }

              return (
                <div key={key} className="border border-gray-200 rounded-lg p-4">
                  <div className="text-sm text-gray-500 mb-1">
                    Key: {selectedNamespace}.{key}
                  </div>
                  <div className="font-medium">
                    {translatedText}
                  </div>
                </div>
              );
            })}
          </div>
        </div>

        {/* 带参数的翻译测试 */}
        <div className="bg-white p-6 rounded-lg shadow mt-6">
          <h2 className="text-xl font-semibold mb-4">带参数的翻译测试</h2>
          <div className="space-y-4">
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="text-sm text-gray-500 mb-1">
                credits.insufficient.message
              </div>
              <div className="font-medium">
                {creditsT('insufficient.message', { required: 5, available: 2 })}
              </div>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="text-sm text-gray-500 mb-1">
                credits.insufficient.timeUntilRefresh
              </div>
              <div className="font-medium">
                {creditsT('insufficient.timeUntilRefresh', { hours: 3, minutes: 45 })}
              </div>
            </div>
            
            <div className="border border-gray-200 rounded-lg p-4">
              <div className="text-sm text-gray-500 mb-1">
                generation.errors.processingError
              </div>
              <div className="font-medium">
                {generationT('errors.processingError', { message: 'Network timeout' })}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
