import { type Metadata } from "next";

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>
}): Promise<Metadata> {
  const {locale} = await params;
  
  const isZh = locale === 'zh';
  
  const metadata: Metadata = {
    title: isZh ? '在线图片编辑 - Flux Pix' : 'Online Image Editor - Flux Pix',
    description: isZh 
      ? 'Flux Pix 在线图片编辑器 - 使用 AI 技术快速编辑图片，支持智能去水印、背景替换、对象移除。免费上传，AI 一键处理，支持 JPG、PNG 格式，最大 10MB。立即开始编辑！'
      : 'Flux Pix Online Image Editor - Use AI technology to quickly edit images, supporting smart watermark removal, background replacement, and object removal. Free upload, AI one-click processing, supports JPG, PNG formats, maximum 10MB. Start editing now!',
    keywords: isZh 
      ? ['在线图片编辑', 'AI图片处理', '图片编辑工具', '智能编辑', '图片生成', '免费修图', 'AI修图', '在线P图', '图片处理器', '智能抠图']
      : ['online image editing', 'AI image processing', 'image editing tool', 'smart editing', 'image generation', 'free photo editing', 'AI photo editing', 'online photo editor', 'image processor', 'smart cutout'],
    openGraph: {
      title: isZh ? '在线图片编辑 - Flux Pix' : 'Online Image Editor - Flux Pix',
      description: isZh 
        ? 'Flux Pix 在线图片编辑器 - 使用 AI 技术快速编辑图片，支持智能去水印、背景替换、对象移除。免费上传，AI 一键处理。'
        : 'Flux Pix Online Image Editor - Use AI technology to quickly edit images, supporting smart watermark removal, background replacement, and object removal. Free upload, AI one-click processing.',
      type: 'website',
      url: `https://fluxpix.ai${locale === 'en' ? '' : `/${locale}`}/generate`,
      images: ['/og-image.png'],
      locale: isZh ? 'zh_CN' : 'en_US',
      siteName: 'Flux Pix'
    },
    twitter: {
      card: 'summary_large_image',
      title: isZh ? '在线图片编辑 - Flux Pix' : 'Online Image Editor - Flux Pix',
      description: isZh 
        ? 'Flux Pix 在线图片编辑器 - 使用 AI 技术快速编辑图片，支持智能去水印、背景替换、对象移除。'
        : 'Flux Pix Online Image Editor - Use AI technology to quickly edit images, supporting smart watermark removal, background replacement, and object removal.',
      images: ['/og-image.png']
    },
    alternates: {
      canonical: `https://fluxpix.ai${locale === 'en' ? '' : `/${locale}`}/generate`,
      languages: {
        'en': 'https://fluxpix.ai/generate',
        'zh': 'https://fluxpix.ai/zh/generate',
        'x-default': 'https://fluxpix.ai/generate'
      }
    },
    robots: 'index, follow'
  };
  
  return metadata;
}

export default function GenerateLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <>
      {children}
    </>
  );
}
