"use client";

import { useState, useRef, useCallback } from "react";
import { useTranslations, useLocale } from "next-intl";
import { motion, AnimatePresence } from "motion/react";
import { Button } from "@/components/ui/button";
import { BackgroundBlurs } from "../components/BackgroundBlurs";
import {
  Loader2,
  Download,
  Trash2,
  RotateCcw,
  Sparkles,
  AlertCircle,
  CheckCircle,
  LogIn,
  Coins,
} from "lucide-react";
import { api } from "@/trpc/react";
import { Session } from "next-auth";
import { signIn } from "next-auth/react";
import { Navbar } from "../components/Navbar";
import { ModelSelector } from "@/components/ModelSelector";

interface GeneratePageClientProps {
  session: Session | null;
}

export function GeneratePageClient({ session }: GeneratePageClientProps) {
  const t = useTranslations("Generate");
  const locale = useLocale();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const resultRef = useRef<HTMLDivElement>(null);
  const [uploadedImage, setUploadedImage] = useState<string | null>(null);
  const [prompt, setPrompt] = useState("");
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<string | null>(null);
  const [selectedModelId, setSelectedModelId] = useState<string>("");
  const [toasts, setToasts] = useState<
    Array<{ id: string; type: "success" | "error"; message: string }>
  >([]);

  // 获取用户积分信息
  const { data: userWithCredits, refetch: refetchCredits } = api.credits.getUserWithCredits.useQuery(
    undefined,
    { enabled: !!session }
  );

  // 获取可用模型列表
  const { data: availableModels } = api.creation.getAvailableModels.useQuery({
    category: 'image-editing'
  });

  // 检查当前模型的积分需求
  const { data: creditCheck } = api.creation.checkCreditsForCreation.useQuery({
    modelId: selectedModelId || undefined
  }, {
    enabled: !!session && !!selectedModelId
  });

  const addToast = useCallback((type: "success" | "error", message: string) => {
    const id = Math.random().toString(36).substring(2, 11);
    setToasts((prev) => [...prev, { id, type, message }]);
    setTimeout(() => {
      setToasts((prev) => prev.filter((toast) => toast.id !== id));
    }, 5000);
  }, []);

  const removeToast = useCallback((id: string) => {
    setToasts((prev) => prev.filter((toast) => toast.id !== id));
  }, []);

  // 登录处理函数
  const handleSignIn = useCallback(async () => {
    try {
      await signIn("google", {
        callbackUrl: window.location.href,
        redirect: false,
      });
    } catch (error) {
      console.error("登录失败:", error);
      addToast("error", "登录失败，请重试");
    }
  }, [addToast]);

  const handleFileUpload = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const file = event.target.files?.[0];
      if (!file) return;

      // Validate file type
      if (!file.type.startsWith("image/")) {
        addToast("error", t("errors.invalidFileType"));
        return;
      }

      // Validate file size (10MB limit)
      const maxSize = 10 * 1024 * 1024; // 10MB in bytes
      if (file.size > maxSize) {
        addToast("error", t("errors.fileSizeLimit"));
        return;
      }

      const reader = new FileReader();
      reader.onload = (e) => {
        const result = e.target?.result as string;
        setUploadedImage(result);
        setResult(null); // Clear previous result
        addToast("success", t("success.imageUploaded"));
      };
      reader.readAsDataURL(file);
    },
    [addToast, t],
  );

  // 创建图片处理mutation
  const createMutation = api.creation.create.useMutation({
    onSuccess: (data) => {
      setResult(data.generatedImageUrl);
      addToast("success", t("Generate.success.imageProcessed"));
      // 刷新积分信息
      void refetchCredits();

      // Auto scroll to result after a short delay
      setTimeout(() => {
        if (resultRef.current) {
          resultRef.current.scrollIntoView({
            behavior: "smooth",
            block: "start",
          });
        }
      }, 1200);
    },
    onError: (error) => {
      addToast("error", error.message || t("Generate.errors.processingFailed"));
      // 刷新积分信息（可能已退还）
      void refetchCredits();
    },
    onSettled: () => {
      setIsProcessing(false);
    },
  });

  const processImage = useCallback(async () => {
    if (!uploadedImage || !prompt.trim()) {
      addToast("error", t("errors.uploadAndPrompt"));
      return;
    }

    // 检查积分
    const requiredCredits = creditCheck?.requiredCredits || 1;
    if (!userWithCredits?.credits || userWithCredits.credits.availableCredits < requiredCredits) {
      addToast("error", t("Generate.errors.insufficientCredits"));
      return;
    }

    setIsProcessing(true);
    setResult(null);

    try {
      // 调用创建API
      await createMutation.mutateAsync({
        prompt: prompt.trim(),
        imageUrl: uploadedImage,
        modelId: selectedModelId || undefined,
        locale: locale,
      });
    } catch (error) {
      // 错误已在onError中处理
      console.error("Processing error:", error);
    }
  }, [uploadedImage, prompt, addToast, t, userWithCredits, createMutation]);

  const clearImage = useCallback(() => {
    setUploadedImage(null);
    setResult(null);
    setPrompt("");
    if (fileInputRef.current) {
      fileInputRef.current.value = "";
    }
    addToast("success", "图片已清除");
  }, [addToast, t]);

  const resetToOriginal = useCallback(() => {
    setResult(null);
    addToast("success", "已重置到原始图片");
  }, [addToast, t]);

  const downloadImage = useCallback(
    (imageUrl: string, filename = "edited-image") => {
      if (typeof document !== "undefined") {
        const link = document.createElement("a");
        link.href = imageUrl;
        link.download = `${filename}.jpg`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    },
    [],
  );

  const structuredData = {
    "@context": "https://schema.org",
    "@type": "WebApplication",
    name: "Flux Pix AI Image Editor",
    description:
      "Professional AI-powered image editing tool with smart features",
    url: "https://fluxpix.ai/generate",
    applicationCategory: "MultimediaApplication",
    operatingSystem: "Web Browser",
    offers: {
      "@type": "Offer",
      price: "0",
      priceCurrency: "USD",
    },
    featureList: [
      "AI Image Processing",
      "Smart Watermark Removal",
      "Background Replacement",
      "Object Removal",
      "Image Enhancement",
    ],
  };

  return (
    <div className="relative min-h-screen w-full overflow-hidden bg-gradient-to-br from-rose-50 via-orange-50 to-amber-50">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{
          __html: JSON.stringify(structuredData),
        }}
      />
      <BackgroundBlurs />

      {/* Animated gradient overlay */}
      <div className="absolute inset-0 animate-pulse bg-gradient-to-br from-rose-100/30 via-orange-100/20 to-amber-100/30" />

      {/* Toast notifications */}
      <div className="fixed right-4 top-4 z-50 space-y-2">
        <AnimatePresence>
          {toasts.map((toast) => (
            <motion.div
              key={toast.id}
              initial={{ opacity: 0, x: 100 }}
              animate={{ opacity: 1, x: 0 }}
              exit={{ opacity: 0, x: 100 }}
              className={`flex items-center gap-2 rounded-lg border px-4 py-3 shadow-lg backdrop-blur-sm ${
                toast.type === "success"
                  ? "border-green-400/50 bg-green-500/90 text-white"
                  : "border-red-400/50 bg-red-500/90 text-white"
              }`}
            >
              {toast.type === "success" ? (
                <CheckCircle className="h-5 w-5" />
              ) : (
                <AlertCircle className="h-5 w-5" />
              )}
              <span className="text-sm font-medium">{toast.message}</span>
              <button
                onClick={() => removeToast(toast.id)}
                className="ml-2 text-white/80 hover:text-white"
              >
                ×
              </button>
            </motion.div>
          ))}
        </AnimatePresence>
      </div>

      <Navbar session={session} />

      <div className="container relative z-10 mx-auto px-4 py-8 sm:px-6">
        <div className="mx-auto max-w-4xl">
          {/* Header */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
            className="mb-12 text-center"
          >
            <div className="relative">
              <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-rose-500/10 to-orange-500/10 blur-xl" />
              <div className="relative rounded-2xl border border-rose-200/50 bg-white/80 p-8 shadow-lg backdrop-blur-md">
                <div className="mb-4 flex items-center justify-center gap-3">
                  <div className="rounded-full bg-gradient-to-r from-rose-400 to-orange-400 p-3">
                    <Sparkles className="h-8 w-8 text-white" />
                  </div>
                  <h1 className="bg-gradient-to-r from-rose-600 to-orange-500 bg-clip-text text-4xl font-bold text-transparent">
                    {t("title")}
                  </h1>
                </div>
                <p className="mx-auto max-w-2xl text-xl text-gray-700">
                  {t("subtitle")}
                </p>
              </div>
            </div>
          </motion.div>

          {/* Main Content */}
          <div className="space-y-8">
            {!session ? (
              /* Login Required Section */
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.6, delay: 0.1 }}
                className="relative"
              >
                <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-rose-500/10 to-orange-500/10 blur-xl" />
                <div className="relative rounded-2xl border border-rose-200/50 bg-white/80 p-12 text-center shadow-lg backdrop-blur-md">
                  <div className="space-y-6">
                    <div className="mx-auto flex h-20 w-20 items-center justify-center rounded-full bg-gradient-to-r from-rose-500 to-orange-500">
                      <LogIn className="h-10 w-10 text-white" />
                    </div>
                    <div>
                      <h3 className="mb-3 text-2xl font-bold text-gray-800">
                        请先登录
                      </h3>
                      <p className="mb-6 text-lg text-gray-600">
                        登录后即可开始使用 AI 图片编辑功能
                      </p>
                    </div>
                    <motion.div
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <Button
                        size="lg"
                        onClick={handleSignIn}
                        className="bg-gradient-to-r from-rose-500 to-orange-500 px-8 py-3 font-semibold text-white shadow-lg shadow-rose-500/25 transition-all duration-300 hover:from-rose-400 hover:to-orange-400"
                      >
                        <LogIn className="mr-2 h-5 w-5" />
                        使用 Google 登录
                      </Button>
                    </motion.div>
                  </div>
                </div>
              </motion.div>
            ) : (
              <>
                {/* Upload Section */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.6, delay: 0.1 }}
                  className="relative"
                >
                  <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-rose-500/10 to-orange-500/10 blur-xl" />
                  <div className="relative rounded-2xl border border-rose-200/50 bg-white/80 p-8 shadow-lg backdrop-blur-md">
                    <div className="space-y-6">
                      {/* Credits Display */}
                      {userWithCredits && (
                        <div className="flex items-center justify-between rounded-lg border border-amber-200/50 bg-gradient-to-r from-amber-50 to-orange-50 p-4">
                          <div className="flex items-center gap-3">
                            <div className="flex h-10 w-10 items-center justify-center rounded-full bg-gradient-to-r from-amber-400 to-orange-400">
                              <Coins className="h-5 w-5 text-white" />
                            </div>
                            <div>
                              <p className="text-sm font-medium text-gray-800">
                                {t("credits.currentCredits")}
                              </p>
                              <p className="text-xs text-gray-600">
                                {t(`credits.userType.${userWithCredits.userType}`)}
                              </p>
                            </div>
                          </div>
                          <div className="text-right">
                            <p className="text-2xl font-bold text-amber-600">
                              {userWithCredits.credits?.availableCredits || 0}
                            </p>
                            <p className="text-xs text-gray-600">
                              {creditCheck?.requiredCredits
                                ? `每次编辑消耗 ${creditCheck.requiredCredits} 积分`
                                : t("credits.display.perEdit")
                              }
                            </p>
                          </div>
                        </div>
                      )}

                      {/* Model Selector */}
                      {availableModels && availableModels.length > 0 && (
                        <div className="space-y-2">
                          <p className="text-sm font-medium text-gray-700">
                            选择AI模型
                          </p>
                          <ModelSelector
                            models={availableModels}
                            selectedModelId={selectedModelId || availableModels[0]?.id || ""}
                            onModelSelect={setSelectedModelId}
                            disabled={isProcessing}
                          />
                        </div>
                      )}

                      {/* Image Upload Area */}
                      <div className="space-y-6">
                        {!uploadedImage ? (
                          <div
                            className="cursor-pointer rounded-xl border-2 border-dashed border-rose-300/50 bg-white/60 p-8 text-center backdrop-blur-sm transition-colors hover:border-rose-400/50"
                            onClick={() => fileInputRef.current?.click()}
                          >
                            <div className="space-y-4">
                              <div className="mx-auto flex h-16 w-16 items-center justify-center rounded-full bg-gradient-to-r from-rose-500 to-orange-500">
                                <Sparkles className="h-8 w-8 text-white" />
                              </div>
                              <div>
                                <p className="text-lg font-medium text-gray-800">
                                  {t("image.dropOrClick")}
                                </p>
                                <p className="mt-2 text-sm text-gray-600">
                                  {t("image.formats")}
                                </p>
                                <p className="mt-1 text-xs text-gray-600">
                                  {t("image.maxSize")}
                                </p>
                              </div>
                            </div>
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="image/*"
                              onChange={handleFileUpload}
                              className="hidden"
                            />
                          </div>
                        ) : (
                          <div className="space-y-4">
                            <div className="relative overflow-hidden rounded-xl border border-rose-300/50 bg-white/60 backdrop-blur-sm">
                              <img
                                src={uploadedImage}
                                alt="Uploaded"
                                className="h-64 w-full object-cover"
                              />
                              <div className="absolute right-3 top-3 flex gap-2">
                                <Button
                                  size="sm"
                                  variant="secondary"
                                  onClick={() => fileInputRef.current?.click()}
                                  className="bg-background/80 backdrop-blur-sm"
                                >
                                  <RotateCcw className="mr-1 h-4 w-4" />
                                  更换
                                </Button>
                                <Button
                                  size="sm"
                                  variant="destructive"
                                  onClick={clearImage}
                                  className="bg-red-500/80 backdrop-blur-sm"
                                >
                                  <Trash2 className="h-4 w-4" />
                                </Button>
                              </div>
                            </div>
                            <input
                              ref={fileInputRef}
                              type="file"
                              accept="image/*"
                              onChange={handleFileUpload}
                              className="hidden"
                            />
                          </div>
                        )}

                        {/* Instruction Input and Generate Button */}
                        <div className="flex gap-4">
                          <input
                            type="text"
                            placeholder={t("instructions.label")}
                            value={prompt}
                            onChange={(e) => setPrompt(e.target.value)}
                            className="h-12 flex-1 rounded-xl border border-muted-foreground/20 bg-gradient-to-br from-background/85 to-background/70 px-4 text-base outline-none backdrop-blur-sm transition-all duration-200 focus:border-rose-400/60"
                            disabled={isProcessing}
                          />
                          <motion.div
                            whileHover={{ scale: 1.02 }}
                            whileTap={{ scale: 0.98 }}
                          >
                            <Button
                              size="lg"
                              disabled={
                                !prompt.trim() ||
                                isProcessing ||
                                !userWithCredits?.credits ||
                                userWithCredits.credits.availableCredits < (creditCheck?.requiredCredits || 1)
                              }
                              onClick={processImage}
                              className="h-12 whitespace-nowrap bg-gradient-to-r from-rose-500 to-orange-500 px-8 font-semibold text-white shadow-lg shadow-rose-500/25 transition-all duration-300 hover:from-rose-400 hover:to-orange-400 disabled:opacity-50"
                            >
                              {isProcessing ? (
                                <>
                                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                                  {t("status.processing")}
                                </>
                              ) : userWithCredits?.credits && userWithCredits.credits.availableCredits < (creditCheck?.requiredCredits || 1) ? (
                                <>
                                  <Coins className="mr-2 h-5 w-5" />
                                  {t("credits.insufficient.title")}
                                </>
                              ) : (
                                <>
                                  <Sparkles className="mr-2 h-5 w-5" />
                                  {t("actions.generate")}
                                </>
                              )}
                            </Button>
                          </motion.div>
                        </div>
                      </div>
                    </div>
                  </div>
                </motion.div>

                {/* Result Section */}
                {(result || isProcessing) && (
                  <motion.div
                    ref={resultRef}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.6 }}
                    className="relative"
                  >
                    <div className="absolute inset-0 rounded-2xl bg-gradient-to-r from-green-500/5 to-rose-500/5 blur-xl" />
                    <div className="relative rounded-2xl border border-rose-300/50 bg-white/70 p-8 backdrop-blur-md">
                      <div className="space-y-6">
                        <h3 className="flex items-center gap-3 text-2xl font-bold text-gray-800">
                          <div className="flex h-8 w-8 items-center justify-center rounded-full bg-gradient-to-r from-green-500 to-rose-500 text-sm font-bold text-white">
                            ✓
                          </div>
                          编辑结果
                        </h3>

                        {isProcessing ? (
                          <div className="flex flex-col items-center justify-center space-y-4 py-16">
                            <div className="relative">
                              <div className="h-16 w-16 animate-spin rounded-full border-4 border-rose-200 border-t-rose-500"></div>
                              <div className="absolute inset-0 flex items-center justify-center">
                                <Sparkles className="h-6 w-6 text-rose-500" />
                              </div>
                            </div>
                            <div className="text-center">
                              <p className="text-lg font-medium text-gray-800">
                                {t("status.processing")}
                              </p>
                              <p className="mt-1 text-sm text-gray-600">
                                {t("instructions.processingText")}
                              </p>
                            </div>
                          </div>
                        ) : result ? (
                          <div className="space-y-6">
                            {/* Before/After Comparison */}
                            <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
                              {/* Original */}
                              <div className="space-y-3">
                                <h4 className="text-lg font-semibold text-gray-800">
                                  {t("image.original")}
                                </h4>
                                <div className="relative overflow-hidden rounded-xl border border-rose-300/50 bg-white/60 backdrop-blur-sm">
                                  <img
                                    src={uploadedImage!}
                                    alt="Original"
                                    className="h-64 w-full object-cover"
                                  />
                                </div>
                              </div>

                              {/* Result */}
                              <div className="space-y-3">
                                <h4 className="text-lg font-semibold text-gray-800">
                                  {t("image.result")}
                                </h4>
                                <div className="relative overflow-hidden rounded-xl border border-rose-300/50 bg-white/60 backdrop-blur-sm">
                                  <img
                                    src={result}
                                    alt="Edited"
                                    className="h-64 w-full object-cover"
                                  />
                                  <div className="absolute right-3 top-3">
                                    <div className="rounded-full bg-gradient-to-r from-green-500 to-rose-500 px-3 py-1 text-sm font-medium text-white">
                                      新
                                    </div>
                                  </div>
                                </div>
                              </div>
                            </div>

                            {/* Action Buttons */}
                            <div className="flex flex-wrap justify-center gap-4">
                              <Button
                                onClick={() =>
                                  downloadImage(result, "edited-image")
                                }
                                className="bg-gradient-to-r from-green-500 to-rose-500 shadow-lg shadow-green-500/25 hover:from-green-400 hover:to-rose-400"
                              >
                                <Download className="mr-2 h-4 w-4" />
                                {t("actions.download")}
                              </Button>
                              <Button
                                variant="outline"
                                onClick={resetToOriginal}
                                className="border-orange-400/50 bg-orange-600/10 text-orange-600 hover:bg-orange-600/20"
                              >
                                <RotateCcw className="mr-2 h-4 w-4" />
                                {t("actions.reset")}
                              </Button>
                              <Button
                                variant="outline"
                                onClick={clearImage}
                                className="border-red-400/50 bg-red-600/10 text-red-600 hover:bg-red-600/20"
                              >
                                <Trash2 className="mr-2 h-4 w-4" />
                                {t("actions.clear")}
                              </Button>
                            </div>
                          </div>
                        ) : null}
                      </div>
                    </div>
                  </motion.div>
                )}
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
