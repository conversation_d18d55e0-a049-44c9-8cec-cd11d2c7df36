"use client";

import { api } from "@/trpc/react";
import { useTranslations } from "next-intl";
import { Coins, Gift, Users, Star, Check, X } from "lucide-react";

export default function TestCreditsConfigPage() {
  const t = useTranslations("credits");
  
  // 获取所有积分配置
  const { data: creditConfigs, isLoading } = api.credits.getCreditConfigs.useQuery();

  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-rose-500 mx-auto mb-4"></div>
          <p>加载积分配置中...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-8">
      <div className="max-w-6xl mx-auto">
        <h1 className="text-3xl font-bold mb-8 flex items-center gap-3">
          <Coins className="h-8 w-8 text-amber-500" />
          积分配置系统测试
        </h1>
        
        {/* 配置概览 */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
          {creditConfigs?.map((config) => (
            <div 
              key={config.userType} 
              className={`bg-white rounded-xl shadow-lg p-6 border-2 ${
                config.userType === 'free' 
                  ? 'border-gray-200' 
                  : config.userType === 'premium'
                  ? 'border-blue-200'
                  : 'border-purple-200'
              }`}
            >
              {/* 用户类型标题 */}
              <div className="flex items-center justify-between mb-4">
                <h2 className="text-xl font-bold">
                  {t(`userType.${config.userType}`)}
                </h2>
                <div className={`px-3 py-1 rounded-full text-xs font-medium ${
                  config.userType === 'free' 
                    ? 'bg-gray-100 text-gray-700' 
                    : config.userType === 'premium'
                    ? 'bg-blue-100 text-blue-700'
                    : 'bg-purple-100 text-purple-700'
                }`}>
                  {config.userType.toUpperCase()}
                </div>
              </div>

              {/* 积分信息 */}
              <div className="space-y-3 mb-6">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">每日积分</span>
                  <div className="flex items-center gap-1">
                    <Coins className="h-4 w-4 text-amber-500" />
                    <span className="font-semibold">{config.dailyCredits}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">最大积分</span>
                  <div className="flex items-center gap-1">
                    <Coins className="h-4 w-4 text-amber-500" />
                    <span className="font-semibold">{config.maxDailyCredits}</span>
                  </div>
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">新用户奖励</span>
                  <div className="flex items-center gap-1">
                    <Gift className="h-4 w-4 text-green-500" />
                    <span className="font-semibold">{config.newUserBonus}</span>
                  </div>
                </div>
              </div>

              {/* 功能特性 */}
              <div className="space-y-2">
                <h3 className="text-sm font-medium text-gray-700 mb-2">功能特性</h3>
                
                <div className="flex items-center justify-between text-xs">
                  <span>私有保存</span>
                  {config.features.canSavePrivate ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span>商业使用</span>
                  {config.features.canUseCommercially ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span>广告显示</span>
                  {config.features.hasAds ? (
                    <X className="h-4 w-4 text-red-500" />
                  ) : (
                    <Check className="h-4 w-4 text-green-500" />
                  )}
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span>存储天数</span>
                  <span className="font-medium">
                    {config.features.maxStorageDays === -1 
                      ? '永久' 
                      : `${config.features.maxStorageDays}天`
                    }
                  </span>
                </div>
                
                <div className="flex items-center justify-between text-xs">
                  <span>优先支持</span>
                  {config.features.prioritySupport ? (
                    <Check className="h-4 w-4 text-green-500" />
                  ) : (
                    <X className="h-4 w-4 text-red-500" />
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* 详细配置表格 */}
        <div className="bg-white rounded-xl shadow-lg overflow-hidden">
          <div className="p-6 border-b border-gray-200">
            <h2 className="text-xl font-bold">详细配置对比</h2>
          </div>
          
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-gray-50">
                <tr>
                  <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                    配置项
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    免费版
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    高级版
                  </th>
                  <th className="px-6 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                    专业版
                  </th>
                </tr>
              </thead>
              <tbody className="bg-white divide-y divide-gray-200">
                {creditConfigs && (
                  <>
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        每日积分
                      </td>
                      {creditConfigs.map((config) => (
                        <td key={config.userType} className="px-6 py-4 whitespace-nowrap text-sm text-center">
                          <div className="flex items-center justify-center gap-1">
                            <Coins className="h-4 w-4 text-amber-500" />
                            <span className="font-semibold">{config.dailyCredits}</span>
                          </div>
                        </td>
                      ))}
                    </tr>
                    
                    <tr className="bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        最大积分
                      </td>
                      {creditConfigs.map((config) => (
                        <td key={config.userType} className="px-6 py-4 whitespace-nowrap text-sm text-center">
                          <span className="font-semibold">{config.maxDailyCredits}</span>
                        </td>
                      ))}
                    </tr>
                    
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        新用户奖励
                      </td>
                      {creditConfigs.map((config) => (
                        <td key={config.userType} className="px-6 py-4 whitespace-nowrap text-sm text-center">
                          <div className="flex items-center justify-center gap-1">
                            <Gift className="h-4 w-4 text-green-500" />
                            <span className="font-semibold">{config.newUserBonus}</span>
                          </div>
                        </td>
                      ))}
                    </tr>
                    
                    <tr className="bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        私有保存
                      </td>
                      {creditConfigs.map((config) => (
                        <td key={config.userType} className="px-6 py-4 whitespace-nowrap text-sm text-center">
                          {config.features.canSavePrivate ? (
                            <Check className="h-5 w-5 text-green-500 mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-red-500 mx-auto" />
                          )}
                        </td>
                      ))}
                    </tr>
                    
                    <tr>
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        商业使用
                      </td>
                      {creditConfigs.map((config) => (
                        <td key={config.userType} className="px-6 py-4 whitespace-nowrap text-sm text-center">
                          {config.features.canUseCommercially ? (
                            <Check className="h-5 w-5 text-green-500 mx-auto" />
                          ) : (
                            <X className="h-5 w-5 text-red-500 mx-auto" />
                          )}
                        </td>
                      ))}
                    </tr>
                    
                    <tr className="bg-gray-50">
                      <td className="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        存储天数
                      </td>
                      {creditConfigs.map((config) => (
                        <td key={config.userType} className="px-6 py-4 whitespace-nowrap text-sm text-center font-semibold">
                          {config.features.maxStorageDays === -1 
                            ? '永久' 
                            : `${config.features.maxStorageDays}天`
                          }
                        </td>
                      ))}
                    </tr>
                  </>
                )}
              </tbody>
            </table>
          </div>
        </div>

        {/* 原始数据 */}
        <div className="bg-white rounded-xl shadow-lg p-6 mt-8">
          <h2 className="text-xl font-bold mb-4">原始配置数据</h2>
          <pre className="bg-gray-100 p-4 rounded-lg text-sm overflow-x-auto">
            {JSON.stringify(creditConfigs, null, 2)}
          </pre>
        </div>
      </div>
    </div>
  );
}
