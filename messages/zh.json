{"navbar": {"features": "功能特性", "pricing": "价格", "gallery": "案例展示", "login": "登录", "signup": "注册", "dashboard": "控制台", "openMenu": "打开菜单", "closeMenu": "关闭菜单", "logout": "退出", "loginWithGoogle": "使用 Google 登录", "generate": "生成", "profile": "个人信息", "signOut": "退出", "signInWithGoogle": "使用 Google 登录"}, "hero": {"title": "AI 图片编辑", "subtitle": "用自然语言描述，AI 帮你编辑图片", "getStartedButton": "开始编辑", "learnMoreButton": "查看案例", "typingText": "描述你想要的效果，AI 帮你实现", "typingMainPart": "描述你想要的效果，", "features": {"removeWatermark": {"title": "智能去水印", "description": "一键去除图片中的水印和不需要的元素"}, "backgroundChange": {"title": "背景替换", "description": "轻松更换图片背景，支持任意场景描述"}, "objectRemoval": {"title": "物体移除", "description": "精准移除图片中的任意物体，自动填补背景"}}}, "features": {"sectionTitle": "编辑功能", "sectionSubtitle": "简单描述，轻松编辑", "removeWatermark": {"title": "智能去水印", "description": "自动识别并去除图片中的水印、标志和不需要的文字，保持图片自然效果", "details": {"0": "智能识别各种水印类型", "1": "保持图片原始质量", "2": "支持批量处理"}}, "backgroundEdit": {"title": "背景编辑", "description": "更换背景、移除背景或添加新场景，支持任意背景描述", "details": {"0": "一键更换背景", "1": "智能边缘检测", "2": "多种背景模板"}}, "objectRemoval": {"title": "物体移除", "description": "精准移除图片中的任意物体、人物或元素，智能填补空白区域", "details": {"0": "精确移除不需要的物体", "1": "自动填充背景", "2": "无痕迹处理"}}, "faceRetouch": {"title": "人像美化", "description": "自然的人像美化效果，去除瑕疵、调整肤色、优化五官", "details": {"0": "自然美颜效果", "1": "保持面部特征", "2": "专业级修饰"}}, "styleTransfer": {"title": "风格转换", "description": "将图片转换为不同艺术风格，如油画、水彩、素描等", "details": {"0": "艺术风格转换", "1": "多种滤镜效果", "2": "创意图像生成"}}, "textReplacement": {"title": "文字替换", "description": "智能识别并替换图片中的文字内容，保持原有字体和样式", "details": {"0": "智能文字识别", "1": "无缝文字替换", "2": "保持字体风格"}}, "ctaTitle": "准备开始编辑您的图片了吗？", "ctaSubtitle": "只需上传图片，输入您的编辑需求，AI将为您完成专业级的图片处理", "ctaButton": "立即开始编辑"}, "cta": {"title": "开始编辑图片", "subtitle": "上传图片，描述效果，AI 帮你实现", "signupButton": "立即开始", "learnMoreButton": "了解更多", "loginHint": "无需注册，直接开始"}, "pricing": {"sectionTitle": "简单透明的价格方案", "sectionSubtitle": "选择最适合您需求的套餐，所有方案均包含核心功能", "popularLabel": "最受欢迎", "freePlan": {"title": "免费版", "price": "¥0", "description": "适合初次体验和创意探索", "buttonText": "开始使用", "credits": "10 积分/天", "features": {"dailyCredits": "每天 10 积分", "trialPeriod": "试用 14 天全模型", "resultStorage": "结果保存一个月", "privateResults": "不能保存为私有", "commercialUse": "不可商业用途", "advertisements": "含广告"}}, "basicPlan": {"title": "基础版", "price": "¥9.9/月", "description": "适合个人创作者和小型项目", "buttonText": "选择基础版", "credits": "400 积分/月", "features": {"monthlyCredits": "400 积分/月", "noAds": "免广告", "longTermStorage": "长期保存结果", "privateResults": "可私有生成结果", "commercialUse": "商业用途", "unlimitedPrompts": "无限制智能提示词"}}, "proPlan": {"title": "高级版", "price": "¥29.9/月", "description": "适合专业创作者和商业用途", "buttonText": "选择高级版", "credits": "1300 积分/月", "features": {"monthlyCredits": "1300 积分/月", "unlimitedPrompts": "无限制智能提示词", "noAds": "免广告", "longTermStorage": "长期保存结果", "privateResults": "可私有生成结果", "commercialUse": "商业用途"}}}, "models": {"sectionTitle": "强大的 Flux 模型系列", "sectionSubtitle": "从基础创作到专业级图像生成，Flux 模型系列满足各种创作需求", "proLabel": "专业版", "ultraLabel": "旗舰版", "fluxSchnell": {"title": "Flux Schnell", "description": "快速生成基础图像，适合概念验证和创意探索"}, "fluxDev": {"title": "Flux Dev", "description": "开发者友好的模型，支持基础图像生成和简单编辑"}, "fluxPro": {"title": "Flux 1.1 Pro", "description": "更快、更好的专业级图像生成，支持高质量创作"}, "fluxUltra": {"title": "Flux 1.1 Ultra", "description": "超高分辨率和真实感，适合专业级创作和商业用途"}, "fluxFill": {"title": "Flux Fill", "description": "强大的图像编辑和扩展功能，支持精确修改和内容填充"}, "fluxDepthCanny": {"title": "Flux Depth/Canny", "description": "深度图提取和边缘训练，支持精确控制图像生成过程"}}, "gallery": {"sectionTitle": "编辑案例", "sectionSubtitle": "查看真实编辑效果", "viewMoreButton": "查看更多", "beforeAfter": {"before": "编辑前", "after": "编辑后", "hoverToCompare": "悬停查看对比"}}, "footer": {"product": "产品", "features": "功能特性", "pricing": "价格", "gallery": "作品展示", "resources": "资源", "documentation": "文档", "tutorials": "教程", "blog": "博客", "company": "公司", "about": "关于我们", "careers": "招聘", "contact": "联系我们", "legal": "法律", "terms": "服务条款", "privacy": "隐私政策", "cookies": "<PERSON><PERSON> 政策", "copyright": "保留所有权利。"}, "locale": {"switchLanguage": "切换语言", "languages": {"zh": "中文", "en": "英文"}}, "labels": {"pro": "专业版"}, "generate": {"title": "Flux 图像生成", "textToImage": "文本生成图像", "textToImageShort": "文本生成", "imageToImage": "图像编辑", "imageToImageShort": "图像编辑", "prompt": "提示词", "promptDescription": "描述您想要生成的图像内容", "clickToChangeImage": "点击更换图片", "dragAndDrop": "拖拽图片到此处或点击上传", "supportedFormats": "支持 JPG、PNG 格式", "textToImagePlaceholder": "输入详细的图像描述，例如：一只可爱的柴犬坐在草地上，阳光明媚，高清摄影风格...", "imageToImagePlaceholder": "描述您想要对图像进行的修改，例如：将背景改为海滩场景，添加日落效果...", "smartOptimize": "智能优化", "optimizing": "优化中...", "negativePrompt": "负面提示词", "negativePromptPlaceholder": "输入不希望在图像中出现的元素，例如：模糊，低质量，变形，不自然...", "modelSelection": "模型选择", "advancedSettings": "高级设置", "hideAdvancedSettings": "隐藏高级设置", "generateImage": "生成图像", "generating": "生成中...", "imageSize": "图像尺寸", "width": "宽度", "height": "高度", "generationControl": "生成控制", "generationCount": "生成数量", "sheets": "张", "samplingSteps": "采样步数", "steps": "步", "guidanceScale": "引导系数", "randomSeed": "随机种子", "generationResults": "生成结果", "generatingImages": "正在生成您的图像，请稍候...", "imagesWillAppearHere": "您生成的图像将显示在这里", "generatedImageAlt": "生成的图像 {index}", "uploadedImageAlt": "上传的图片", "selectModelPlaceholder": "选择模型"}, "Generate": {"aiPowered": "AI 智能图像处理", "errors": {"uploadAndPrompt": "请上传图片并输入提示词", "processingFailed": "处理失败，请重试", "uploadFailed": "上传失败，请重试", "uploadUrlFailed": "获取上传链接失败，请重试", "invalidFileType": "请上传有效的图片文件 (JPG, JPEG, PNG)", "fileSizeLimit": "文件大小不能超过 10MB", "dropValidImages": "请拖放有效的图片文件"}, "success": {"imageProcessed": "图片处理成功！", "imageUploaded": "图片上传成功！"}, "title": "AI 智能图像编辑", "subtitle": "上传图片，用自然语言描述编辑需求，AI 帮您实现专业级图像处理", "processing": "AI 正在处理您的图像...", "generate": "开始 AI 编辑", "features": {"title": "编辑功能", "description": "选择一项功能快速编辑图片", "enhance": {"name": "增强", "template": "提升图片质量，使其看起来更专业"}, "retouch": {"name": "修饰", "template": "去除人像中的瑕疵和不完美之处"}, "background": {"name": "背景", "template": "将背景更改为"}, "style": {"name": "风格", "template": "使用风格转换图片"}, "resize": {"name": "调整大小", "template": "在保持质量的同时调整图片大小"}}, "prompt": {"label": "编辑提示词", "placeholder": "详细描述您想要的编辑效果，例如：'移除背景'、'更换为海滩背景'、'增强画质'、'添加日落效果'...", "help": "请具体说明您想要进行的更改", "suggestions": {"title": "快速建议", "hide": "隐藏建议", "show": "显示建议"}}, "advanced": {"title": "高级选项", "quality": "输出质量", "preserve": "保留原始特征", "format": "输出格式", "enhance": "增强细节"}, "image": {"title": "图片", "description": "上传图片进行编辑", "upload": "拖拽图片到此处或点击上传", "dropOrClick": "拖拽图片到此处或点击上传", "maxSize": "最大 10MB", "formats": "支持 JPG、PNG、WEBP、GIF 格式，最大 10MB", "selectButton": "选择图片文件", "original": "原始图片", "result": "编辑结果", "comparison": "对比视图", "single": "单视图"}, "instructions": {"label": "编辑指令", "processingText": "正在处理您的图片..."}, "suggestions": {"title": "快捷指令", "removeBackground": "移除背景", "enhanceQuality": "提升画质", "portraitRetouch": "人像美化", "changeBackground": "更换背景", "artisticStyle": "艺术风格", "vintageEffect": "复古效果"}, "actions": {"download": "下载", "clear": "清除", "reset": "重置", "generate": "开始编辑"}, "status": {"uploading": "上传中...", "processing": "正在处理您的图片...", "complete": "处理完成！", "error": "出现错误，请重试。"}, "batch": {"title": "批量处理", "description": "一次处理多张图片", "limit": "最多 5 张图片"}}, "privacy": {"title": "隐私政策", "lastUpdated": "最后更新", "dataCollection": {"title": "信息收集", "intro": "我们收集以下类型的信息以提供和改进我们的服务：", "personal": {"title": "个人信息", "email": "电子邮件地址（用于账户创建和通信）", "name": "姓名或用户名", "profile": "个人资料信息（如头像）"}, "usage": {"title": "使用数据", "images": "上传的图片和编辑结果", "prompts": "编辑指令和提示词", "activity": "使用活动和偏好设置"}}, "dataUsage": {"title": "信息使用", "intro": "我们使用收集的信息用于以下目的：", "service": "提供图片编辑服务和功能", "improvement": "改进和优化我们的AI模型和服务质量", "support": "提供客户支持和技术帮助", "communication": "发送服务相关通知和更新", "legal": "遵守法律要求和保护我们的权利"}, "dataSharing": {"title": "信息共享", "intro": "我们不会出售您的个人信息。我们仅在以下情况下共享信息：", "consent": "获得您的明确同意", "providers": "与可信的第三方服务提供商（如云存储、支付处理）", "legal": "法律要求或保护合法权益", "business": "业务转让或合并情况下"}, "dataSecurity": {"title": "数据安全", "intro": "我们采取多种安全措施保护您的信息：", "encryption": "数据传输和存储加密", "access": "严格的访问控制和权限管理", "monitoring": "定期安全监控和漏洞扫描", "backup": "安全的数据备份和恢复机制"}, "userRights": {"title": "用户权利", "intro": "您对自己的个人信息享有以下权利：", "access": "访问和查看您的个人数据", "correction": "更正不准确或不完整的信息", "deletion": "请求删除您的个人数据", "portability": "获取您的数据副本", "objection": "反对某些数据处理活动"}, "cookies": {"title": "<PERSON><PERSON> 和跟踪技术", "intro": "我们使用Cookie和类似技术来改善用户体验：", "essential": {"title": "必要<PERSON><PERSON>", "description": "确保网站正常运行所必需的Cookie"}, "analytics": {"title": "分析<PERSON><PERSON>", "description": "帮助我们了解网站使用情况和改进服务"}}, "thirdParty": {"title": "第三方服务", "intro": "我们使用以下第三方服务，它们有自己的隐私政策：", "auth": "Google 认证服务", "storage": "云存储服务（用于图片存储）", "analytics": "网站分析服务", "payment": "支付处理服务"}, "dataRetention": {"title": "数据保留", "intro": "我们按照以下政策保留您的数据：", "account": "账户数据：账户存在期间", "images": "图片数据：根据您的订阅计划", "logs": "日志数据：最多保留12个月"}, "childrenPrivacy": {"title": "儿童隐私", "content": "我们的服务不面向13岁以下儿童。我们不会故意收集13岁以下儿童的个人信息。如果我们发现收集了此类信息，将立即删除。"}, "policyUpdates": {"title": "政策更新", "content": "我们可能会不时更新此隐私政策。重大变更将通过电子邮件或网站通知您。继续使用我们的服务即表示您接受更新后的政策。"}, "contact": {"title": "联系我们", "intro": "如果您对此隐私政策有任何问题或疑虑，请联系我们：", "email": {"label": "电子邮件"}, "address": {"label": "地址", "value": "FluxPix AI 智能科技，上海市浦东新区张江高科技园区 200120"}}}, "terms": {"title": "服务条款", "lastUpdated": "最后更新", "serviceAgreement": {"title": "服务协议", "intro": "欢迎使用我们的AI图片编辑服务。使用我们的服务即表示您同意以下条款：", "acceptance": "使用服务即表示接受本条款", "changes": "我们保留随时修改条款的权利", "eligibility": "用户必须年满18岁或获得监护人同意"}, "serviceDescription": {"title": "服务描述", "intro": "我们提供基于人工智能的图片编辑服务，包括但不限于：", "editing": "图片编辑和处理功能", "ai": "AI驱动的自动化编辑工具", "storage": "云端图片存储和管理", "support": "客户支持和技术帮助"}, "userResponsibilities": {"title": "用户责任", "intro": "作为用户，您有责任：", "lawful": "合法使用我们的服务", "rights": "确保您拥有上传内容的合法权利", "content": "不上传违法、有害或侵权内容", "security": "保护您的账户安全和登录信息"}, "prohibitedUse": {"title": "禁止使用", "intro": "以下行为被严格禁止：", "illegal": "任何违法或不当的活动", "harmful": "上传有害、诽谤或冒犯性内容", "infringing": "侵犯他人知识产权的行为", "malicious": "恶意攻击或破坏服务的行为", "spam": "发送垃圾信息或进行商业推广"}, "intellectualProperty": {"title": "知识产权", "intro": "知识产权保护是我们服务的重要组成部分：", "ourRights": {"title": "我们的权利", "description": "我们拥有服务、软件、技术和相关知识产权的所有权利"}, "userContent": {"title": "用户内容", "description": "您保留对上传内容的所有权，但授予我们处理和存储的必要权限"}}, "paidServices": {"title": "付费服务", "intro": "对于付费服务，以下条款适用：", "billing": "费用将按照选定的计划自动收取", "refunds": "退款政策根据具体情况而定", "cancellation": "您可以随时取消订阅", "changes": "我们保留调整价格的权利，但会提前通知"}, "serviceAvailability": {"title": "服务可用性", "content": "我们努力保持服务的持续可用性，但不保证100%的正常运行时间。我们可能因维护、更新或其他原因暂时中断服务。"}, "disclaimer": {"title": "免责声明", "content": "我们的服务按\"现状\"提供，不提供任何明示或暗示的保证。我们不保证服务的准确性、可靠性或适用性。"}, "limitationOfLiability": {"title": "责任限制", "content": "在法律允许的最大范围内，我们不对任何间接、偶然、特殊或后果性损害承担责任。我们的总责任不超过您支付给我们的费用。"}, "termination": {"title": "服务终止", "intro": "服务可能在以下情况下终止：", "userTermination": "您可以随时删除账户终止服务", "ourTermination": "我们可能因违反条款而终止您的账户", "effect": "终止后，您的数据可能被删除"}, "governingLaw": {"title": "适用法律", "content": "本条款受中华人民共和国法律管辖。任何争议将通过友好协商解决，如无法协商解决，将提交有管辖权的法院处理。"}, "contact": {"title": "联系我们", "intro": "如果您对服务条款有任何问题，请联系我们：", "email": {"label": "电子邮件"}, "address": {"label": "地址", "value": "FluxPix AI 智能科技，上海市浦东新区张江高科技园区 200120"}}}, "common": {"home": "首页", "loading": "加载中...", "error": "错误", "success": "成功", "cancel": "取消", "confirm": "确认", "close": "关闭", "save": "保存", "edit": "编辑", "delete": "删除", "back": "返回", "next": "下一步", "previous": "上一步", "submit": "提交"}, "notFound": {"title": "页面未找到", "description": "抱歉，您访问的页面不存在或已被移动。", "backToHome": "返回首页", "startEditing": "开始编辑", "helpfulLinks": {"title": "常用功能", "aiImageEditing": "AI 图片编辑", "smartWatermarkRemoval": "智能去水印", "backgroundReplacement": "背景替换"}, "supportLinks": {"title": "帮助信息", "privacyPolicy": "隐私政策", "termsOfService": "服务条款", "contactSupport": "联系支持"}}, "profile": {"title": "个人资料", "subtitle": "管理您的账户信息和设置", "loading": "加载中...", "welcome": "欢迎使用 Flux Pix", "anonymousUser": "匿名用户", "email": "邮箱地址", "name": "用户名", "joinedDate": "注册时间", "notSet": "未设置", "signOut": "退出登录"}, "credits": {"title": "积分管理", "currentCredits": "当前积分", "totalCredits": "总积分", "usedCredits": "已使用积分", "availableCredits": "可用积分", "dailyCredits": "每日积分", "lastRefresh": "上次刷新", "userType": {"title": "用户类型", "free": "免费用户", "premium": "高级用户", "pro": "专业用户"}, "history": {"title": "积分历史", "type": {"daily_grant": "每日赠送", "usage": "消耗", "refund": "退还", "purchase": "购买", "bonus": "奖励"}, "description": {"dailyRefresh": "每日积分刷新", "aiImageEdit": "AI图片编辑", "aiImageEditFailed": "AI图片编辑失败，积分退还", "newUserBonus": "新用户每日积分"}}, "insufficient": {"title": "积分不足", "message": "此操作需要 {required} 积分，但您当前只有 {available} 积分。", "dailyRefresh": "每日免费积分", "timeUntilRefresh": "距离下次积分刷新还有 {hours}小时{minutes}分钟", "dailyAmount": "每天可免费获得 10 积分", "tryLater": "稍后再试", "learnMore": "了解更多"}, "display": {"perEdit": "每次编辑消耗 1 积分", "refreshedToday": "今日已刷新"}, "config": {"title": "积分配置", "dailyCredits": "每日积分", "maxDailyCredits": "每日最大积分", "newUserBonus": "新用户奖励", "referralBonus": "推荐奖励", "features": {"canSavePrivate": "私有保存", "canUseCommercially": "商业使用", "hasAds": "广告显示", "maxStorageDays": "存储天数", "prioritySupport": "优先支持", "unlimited": "无限制", "enabled": "启用", "disabled": "禁用"}}}, "generation": {"title": "AI图片生成", "processing": "正在处理您的图片...", "success": "图片处理成功！", "failed": "图片处理失败", "errors": {"invalidPrompt": "编辑指令不能为空", "invalidImageUrl": "图片URL格式不正确", "invalidModel": "无效的AI模型", "aiProcessingFailed": "AI处理失败：未生成图片", "uploadFailed": "图片上传失败", "insufficientCredits": "积分不足，无法进行图片编辑", "processingError": "图片处理失败: {message}", "genericError": "图片处理失败，请稍后重试"}, "result": {"originalImage": "原始图片", "generatedImage": "生成结果", "prompt": "编辑指令", "creditsUsed": "消耗积分", "processingTime": "处理时间", "viewOriginal": "查看原图"}, "status": {"queueUpdate": "队列更新", "aiProcessing": "AI正在处理", "uploadingToStorage": "正在上传到存储", "completed": "处理完成"}}}