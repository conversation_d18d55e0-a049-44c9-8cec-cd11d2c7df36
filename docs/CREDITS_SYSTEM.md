# 积分系统文档

## 概述

本项目实现了一个完整的用户积分系统，支持新用户积分初始化、每日积分刷新、积分消耗和历史记录等功能。

## 功能特性

### 1. 新用户积分初始化
- 新用户首次登录时自动获得10积分
- 积分记录会自动创建并记录到积分历史中

### 2. 每日积分刷新
- 每天自动为用户刷新10积分
- **按需刷新**：在用户请求积分相关接口时自动检查并刷新
- 防重复刷新机制（每天只能刷新一次）
- 无需定时任务，更加简单高效

### 3. 积分消耗
- 支持消耗指定数量的积分
- 自动检查积分余额
- 记录消耗历史和相关操作

### 4. 积分历史
- 完整的积分获得和消耗记录
- 支持按时间排序查询
- 包含操作类型、数量、描述等详细信息

## 数据库结构

### user_credits 表
```sql
- id: 主键
- userId: 用户ID（外键）
- totalCredits: 总积分数
- usedCredits: 已使用积分数
- dailyCredits: 每日积分数（默认10）
- lastDailyRefresh: 上次每日刷新时间
- createdAt: 创建时间
- updatedAt: 更新时间
```

### credit_transactions 表
```sql
- id: 主键
- userId: 用户ID（外键）
- type: 交易类型（daily_grant, usage, purchase, bonus）
- amount: 积分数量（正数为获得，负数为消耗）
- description: 描述
- relatedId: 关联ID（可选）
- createdAt: 创建时间
```

## API 接口

### tRPC 路由 (credits)

#### 1. getUserCredits
获取用户当前积分信息
```typescript
const credits = await api.credits.getUserCredits.useQuery();
```

#### 2. getCreditHistory
获取用户积分历史记录
```typescript
const history = await api.credits.getCreditHistory.useQuery({ limit: 20 });
```

#### 3. consumeCredits
消耗用户积分
```typescript
await api.credits.consumeCredits.mutate({
  amount: 1,
  description: "图片编辑",
  relatedId: "creation-id"
});
```

#### 4. refreshDailyCredits
手动刷新每日积分
```typescript
await api.credits.refreshDailyCredits.mutate();
```



## 使用方法

### 1. 在组件中使用积分系统

```typescript
import { api } from "@/trpc/react";

function MyComponent() {
  // 获取用户积分
  const { data: credits } = api.credits.getUserCredits.useQuery();
  
  // 消耗积分
  const consumeMutation = api.credits.consumeCredits.useMutation({
    onSuccess: () => {
      // 积分消耗成功
    },
    onError: (error) => {
      // 处理错误
    }
  });

  const handleUseCredits = () => {
    consumeMutation.mutate({
      amount: 1,
      description: "AI图片编辑"
    });
  };

  return (
    <div>
      <p>可用积分: {credits?.availableCredits}</p>
      <button onClick={handleUseCredits}>使用1积分</button>
    </div>
  );
}
```

### 2. 服务端使用积分服务

```typescript
import { CreditService } from "@/server/services/creditService";

// 检查用户是否有足够积分
const hasEnough = await CreditService.hasEnoughCredits(userId, 1);

// 消耗积分
const success = await CreditService.consumeCredits(
  userId, 
  1, 
  "AI图片编辑", 
  creationId
);

// 获取用户积分信息
const credits = await CreditService.getUserCredits(userId);
```

## 积分中间件使用

### 在tRPC路由中使用积分中间件

```typescript
import { requireCredits, checkCredits } from "@/server/middleware/creditMiddleware";

// 检查积分但不消耗
const hasEnough = await checkCredits(userId, 1);

// 消耗积分（会自动刷新每日积分）
await requireCredits(userId, 1, "AI图片编辑", creationId);
```

## 测试

访问 `/test-credits` 页面可以测试积分系统的各项功能：
- 查看当前积分信息
- 手动刷新每日积分
- 测试积分消耗
- 查看积分历史

## 注意事项

1. **自动初始化**：积分系统会在用户首次登录时自动初始化
2. **按需刷新**：每日积分在用户请求相关接口时自动检查并刷新，无需定时任务
3. **防重复刷新**：每天只能刷新一次，防止重复获得积分
4. **余额检查**：积分消耗前会自动检查余额，余额不足时会抛出错误
5. **完整记录**：所有积分操作都会记录到历史表中，便于追踪
6. **高效设计**：按需刷新机制避免了定时任务的复杂性，更加简单高效
