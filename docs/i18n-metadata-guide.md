# 智能国际化 Metadata 系统使用指南

## 概述

本项目实现了一个智能的、可扩展的国际化 metadata 系统，可以轻松支持多语言的 SEO 优化。

## 系统架构

### 1. 核心组件

- **`src/lib/metadata.ts`**: 智能 metadata 生成工具
- **`messages/{locale}.json`**: 国际化消息文件（包含 metadata 配置）
- **页面级 `generateMetadata` 函数**: 使用工具函数生成 metadata

### 2. 支持的页面类型

- `home`: 首页
- `generate`: 生成/编辑页面  
- `notFound`: 404 页面

## 如何添加新语言

### 步骤 1: 更新路由配置

在 `src/i18n/routing.ts` 中添加新语言：

```typescript
export const routing = defineRouting({
  locales: ['en', 'zh', 'fr'], // 添加 'fr'
  defaultLocale: 'en',
  localePrefix: 'as-needed'
});
```

### 步骤 2: 创建语言文件

创建 `messages/fr.json`，参考 `messages/fr.json.example`：

```json
{
  "metadata": {
    "home": {
      "title": "Flux Pix - Éditeur d'Images IA Accueil",
      "description": "...",
      "keywords": ["..."]
    },
    "generate": {
      "title": "Éditeur d'Images en Ligne - Flux Pix", 
      "description": "...",
      "keywords": ["..."]
    },
    "notFound": {
      "title": "Page Non Trouvée - Flux Pix",
      "description": "...",
      "keywords": ["..."]
    }
  }
}
```

### 步骤 3: 更新 TypeScript 类型

在相关的类型定义中添加新语言支持。

## 如何添加新页面类型

### 步骤 1: 更新类型定义

在 `src/lib/metadata.ts` 中添加新的页面类型：

```typescript
export type PageType = 'home' | 'generate' | 'notFound' | 'about'; // 添加 'about'
```

### 步骤 2: 在语言文件中添加配置

在所有 `messages/{locale}.json` 文件中添加：

```json
{
  "metadata": {
    "about": {
      "title": "About Us - Flux Pix",
      "description": "Learn more about Flux Pix...",
      "keywords": ["about", "company", "AI image editor"]
    }
  }
}
```

### 步骤 3: 创建便捷函数（可选）

在 `src/lib/metadata.ts` 中添加：

```typescript
export async function generateAboutMetadata(locale: string): Promise<Metadata> {
  return generatePageMetadata(locale, 'about', '/about');
}
```

### 步骤 4: 在页面中使用

```typescript
// src/app/[locale]/about/page.tsx
import { generateAboutMetadata } from "@/lib/metadata";

export async function generateMetadata({
  params
}: {
  params: Promise<{locale: string}>
}): Promise<Metadata> {
  const {locale} = await params;
  return generateAboutMetadata(locale);
}
```

## 系统优势

### 1. 高度可扩展
- 添加新语言只需创建语言文件
- 添加新页面类型只需更新配置
- 无需修改核心逻辑

### 2. 自动化处理
- 自动生成正确的 canonical URL
- 自动生成 hreflang 标签
- 自动处理 OpenGraph 和 Twitter Card

### 3. SEO 优化
- 完整的多语言 SEO 支持
- 结构化数据自动生成
- 正确的语言标记

### 4. 类型安全
- TypeScript 类型检查
- 编译时错误检测
- IDE 智能提示

## 最佳实践

### 1. 翻译质量
- 确保所有语言的 metadata 都经过专业翻译
- 关键词要符合目标语言的搜索习惯
- 描述要自然流畅，避免机器翻译痕迹

### 2. SEO 优化
- 标题长度控制在 60 字符以内
- 描述长度控制在 160 字符以内
- 关键词要相关且不重复

### 3. 维护性
- 定期检查所有语言文件的一致性
- 使用版本控制跟踪翻译变更
- 建立翻译审核流程

## 故障排除

### 常见问题

1. **新语言不显示**: 检查 `routing.ts` 配置和语言文件是否存在
2. **Metadata 不更新**: 清除 Next.js 缓存并重新构建
3. **类型错误**: 确保所有语言文件都包含相同的 metadata 结构

### 调试技巧

- 使用浏览器开发者工具检查生成的 meta 标签
- 使用 Google Search Console 验证 hreflang 标签
- 使用 Facebook Debugger 和 Twitter Card Validator 测试社交媒体分享
